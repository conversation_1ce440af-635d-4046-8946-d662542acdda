/* ========================================
   Nearby Hotels Page - Modern UI/UX Design
   Enhanced with Glass Morphism, Modern Animations, and Professional Typography
   ======================================== */

/* Additional CSS Variables for Enhanced Design */
:root {
    /* Enhanced spacing system */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Enhanced border radius system */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 50%;

    /* Modern color system fallbacks */
    --primary-color: #2E5990;
    --secondary-color: #4CAF50;
    --primary-blue: #2E5990;
    --primary-green: #4CAF50;
}

/* Enhanced Hero Section Styles */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(46, 89, 144, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(255, 140, 66, 0.1) 0%, transparent 50%);
    animation: particleFloat 15s ease-in-out infinite;
}

@keyframes particleFloat {
    0%, 100% { transform: translate(0, 0) scale(1); }
    33% { transform: translate(20px, -20px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-badge i {
    color: var(--primary-blue);
    font-size: var(--font-size-base);
}

.title-highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    font-weight: var(--font-weight-extrabold);
}

.title-main {
    color: white;
    display: block;
    font-weight: var(--font-weight-bold);
    margin-top: 0.5rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-top: 3rem;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    padding: 1.5rem 2rem;
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-xl);
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: white;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.9);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Modern Loading State with Glass Morphism */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 2rem 0;
}

.loading-spinner {
    width: 80px;
    height: 80px;
    border: 6px solid rgba(46, 89, 144, 0.1);
    border-left: 6px solid var(--primary-blue);
    border-radius: 50%;
    animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    margin-bottom: 2rem;
    position: relative;
}

.loading-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(76, 175, 80, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: modernSpin 0.8s cubic-bezier(0.4, 0, 0.2, 1) infinite reverse;
}

@keyframes modernSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-blue);
    border-radius: 50%;
    animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.loading-state p {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Empty State with Enhanced Visual Appeal */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(226, 232, 240, 0.6) 100%);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    border: 2px dashed rgba(46, 89, 144, 0.2);
    position: relative;
    overflow: hidden;
    margin: 2rem 0;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(46, 89, 144, 0.05) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(-20px, -20px) rotate(180deg); }
}

.empty-state i {
    font-size: 6rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.empty-state h3 {
    font-size: var(--font-size-2xl);
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-weight: var(--font-weight-bold);
    position: relative;
    z-index: 1;
}

.empty-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    position: relative;
    z-index: 1;
    max-width: 400px;
}

/* Modern Filter Header with Enhanced Typography */
.filter-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.filter-header::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.filter-title h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    position: relative;
}

.filter-title h2 i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: var(--font-size-xl);
}

.filter-title p {
    color: var(--text-secondary);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Modern Hotels Filter Section with Glass Morphism */
.hotels-filter-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, rgba(248, 251, 255, 0.9) 0%, rgba(232, 244, 253, 0.8) 100%);
    border-bottom: 1px solid rgba(46, 89, 144, 0.1);
    position: relative;
    overflow: hidden;
}

.hotels-filter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(46, 89, 144, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.3' fill-rule='evenodd'%3E%3Cpath d='m0 60l60-60h-60v60zm60 0v-60h-60l60 60z'/%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
    animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(-10px, -10px) scale(1.05); }
}

.hotels-filter-section .container {
    position: relative;
    z-index: 1;
}

/* Modern Filter Controls with Glass Morphism */
.filter-controls {
    display: flex;
    gap: 2rem;
    align-items: end;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 2.5rem;
    border-radius: var(--radius-2xl);
    box-shadow:
        0 25px 50px rgba(46, 89, 144, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(46, 89, 144, 0.1);
    position: relative;
    overflow: hidden;
}

.filter-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 240px;
    flex: 1;
    position: relative;
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-group label i {
    color: var(--primary-blue);
    font-size: var(--font-size-base);
}

/* Modern Filter Select with Enhanced Styling */
.filter-select {
    padding: 1rem 1.5rem;
    border: 2px solid rgba(46, 89, 144, 0.2);
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, rgba(250, 252, 255, 0.9) 0%, rgba(240, 248, 255, 0.8) 100%);
    backdrop-filter: blur(10px);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    transition: all var(--transition-normal);
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232E5990' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: calc(1.5rem + 2em);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.filter-select::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--radius-xl);
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow:
        0 0 0 4px rgba(46, 89, 144, 0.1),
        var(--shadow-lg);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
}

.filter-select:hover {
    border-color: var(--primary-blue);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Modern Reset Button with Enhanced Design */
.reset-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-height: 56px;
    white-space: nowrap;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.reset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.reset-btn:hover::before {
    left: 100%;
}

.reset-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 30px rgba(239, 68, 68, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.reset-btn:active {
    transform: translateY(-1px);
}

/* Modern Filter Info Cards */
.filter-info {
    margin-top: 3rem;
}

.info-cards {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.info-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    padding: 1rem 1.5rem;
    border-radius: var(--radius-xl);
    border: 1px solid rgba(46, 89, 144, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    background: rgba(255, 255, 255, 0.95);
}

.info-card i {
    color: var(--primary-blue);
    font-size: var(--font-size-base);
    width: 20px;
    text-align: center;
}

/* Modern Section Header Styles */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(46, 89, 144, 0.1);
    box-shadow: var(--shadow-md);
}

.hotels-count {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.hotels-count::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 30px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.hotels-count span {
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-extrabold);
    min-width: 50px;
    text-align: center;
    box-shadow: var(--shadow-md);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sort-select {
    padding: 0.75rem 1.25rem;
    border: 2px solid rgba(46, 89, 144, 0.2);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow:
        0 0 0 3px rgba(46, 89, 144, 0.1),
        var(--shadow-md);
    transform: translateY(-2px);
}

.sort-select:hover {
    border-color: var(--primary-blue);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Hotels Grid Section */
.hotels-grid-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
}

.hotels-grid-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.1'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4z'/%3E%3Cpath d='m6 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.hotels-grid-section .container {
    position: relative;
    z-index: 1;
}

.hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Modern Hotel Card with Glass Morphism and Enhanced Design */
.hotel-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(46, 89, 144, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 1px solid rgba(46, 89, 144, 0.1);
    position: relative;
    transform-style: preserve-3d;
}

.hotel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
    z-index: 2;
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.hotel-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(46, 89, 144, 0.02) 0%, rgba(76, 175, 80, 0.02) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
    border-radius: var(--radius-2xl);
}

.hotel-card:hover {
    transform: translateY(-15px) scale(1.03) rotateX(5deg);
    box-shadow:
        0 35px 70px rgba(46, 89, 144, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.hotel-card:hover::after {
    opacity: 1;
}

/* Modern Hotel Image Container with Enhanced Effects */
.hotel-image-container {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(46, 89, 144, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.hotel-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(46, 89, 144, 0.1) 0%,
        transparent 50%,
        rgba(76, 175, 80, 0.1) 100%
    );
    z-index: 2;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.hotel-card:hover .hotel-image-container::before {
    opacity: 1;
}

.hotel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    z-index: 1;
}

.hotel-card:hover .hotel-image {
    transform: scale(1.08) rotate(1deg);
}

/* Modern Hotel Category Badge with Enhanced Styling */
.hotel-category-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(46, 89, 144, 0.95);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    backdrop-filter: blur(20px);
    z-index: 3;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all var(--transition-normal);
    transform: scale(0.95);
}

.hotel-card:hover .hotel-category-badge {
    transform: scale(1);
    box-shadow:
        0 12px 35px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

.hotel-category-badge.luxury {
    background: linear-gradient(135deg, #ffd700 0%, #f39c12 100%);
    color: #1a1a1a;
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow:
        0 8px 30px rgba(255, 215, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

.hotel-category-badge.business {
    background: linear-gradient(135deg, #1e88e5 0%, #3f51b5 100%);
    border-color: rgba(30, 136, 229, 0.6);
    box-shadow:
        0 8px 30px rgba(30, 136, 229, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

.hotel-category-badge.comfort {
    background: linear-gradient(135deg, #26a69a 0%, #009688 100%);
    border-color: rgba(38, 166, 154, 0.6);
    box-shadow:
        0 8px 30px rgba(38, 166, 154, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.3);
}

/* Modern Hotel Content with Enhanced Typography */
.hotel-content {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.hotel-header {
    margin-bottom: 1.5rem;
    position: relative;
}

.hotel-header::after {
    content: '';
    position: absolute;
    bottom: -0.75rem;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

.hotel-card:hover .hotel-header::after {
    width: 100px;
}

.hotel-name {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    line-height: 1.3;
    transition: all var(--transition-normal);
}

.hotel-card:hover .hotel-name {
    transform: translateX(5px);
}

.hotel-rating {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding: 0.75rem 1rem;
    background: rgba(46, 89, 144, 0.05);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-blue);
}

.stars {
    display: flex;
    gap: 3px;
}

.star {
    color: #ffd700;
    font-size: var(--font-size-base);
    text-shadow: 0 1px 3px rgba(255, 215, 0, 0.3);
    transition: transform var(--transition-fast);
}

.hotel-card:hover .star {
    transform: scale(1.1);
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-semibold);
    background: rgba(255, 255, 255, 0.8);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
}

.hotel-distance {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(11, 76, 122, 0.05);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);
}

.distance-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.distance-primary {
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.distance-secondary {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.hotel-features {
    margin-bottom: var(--spacing-lg);
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(30, 136, 229, 0.1);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.feature-icon {
    color: var(--secondary-color);
    font-size: var(--font-size-xs);
}

/* Modern Hotel Actions with Professional Button Design */
.hotel-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 1.5rem;
}

.book-btn {
    flex: 1;
    background: var(--gradient-primary);
    color: white;
    text-decoration: none;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    text-align: center;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: var(--font-size-base);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
}

.book-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.book-btn:hover::before {
    left: 100%;
}

.book-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 30px rgba(46, 89, 144, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    text-decoration: none;
    color: white;
}

.info-btn {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    padding: 1rem;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: var(--font-size-lg);
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.info-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--primary-blue);
    border-radius: 50%;
    transition: all var(--transition-normal);
    transform: translate(-50%, -50%);
}

.info-btn:hover::before {
    width: 100%;
    height: 100%;
}

.info-btn:hover {
    color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.info-btn i {
    position: relative;
    z-index: 1;
}

/* Venue Info Section */
.venue-info-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
}

.venue-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.venue-info-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.venue-info-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;
}

.venue-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    box-shadow: 0 8px 25px rgba(11, 76, 122, 0.3);
}

.venue-details h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.venue-details p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.venue-details i {
    color: var(--secondary-color);
    width: 20px;
}

.venue-actions .venue-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-decoration: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.venue-actions .venue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(11, 76, 122, 0.3);
    text-decoration: none;
    color: white;
}

/* Booking Tips Section */
.booking-tips-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.booking-tips-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.4'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2L74 40h-2zm4 0l4-4v2L78 40h-2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.booking-tips-section .container {
    position: relative;
    z-index: 1;
}

.tips-content {
    max-width: 1000px;
    margin: 0 auto;
}

.tips-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.tips-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 50%;
    margin-bottom: var(--spacing-lg);
    color: white;
    font-size: var(--font-size-2xl);
    box-shadow: 0 15px 35px rgba(251, 191, 36, 0.3);
    position: relative;
}

.tips-icon::before {
    content: '';
    position: absolute;
    inset: -4px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b, #fbbf24);
    border-radius: 50%;
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 0.3; }
}

.tips-title {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.tips-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-xl);
}

.tip-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    text-align: center;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.08);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.tip-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color) 0%, var(--primary-color) 100%);
}

.tip-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(11, 76, 122, 0.15);
    border-color: rgba(30, 136, 229, 0.3);
}

.tip-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    color: white;
    font-size: var(--font-size-xl);
    box-shadow: 0 8px 25px rgba(30, 136, 229, 0.3);
    position: relative;
}

.tip-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
}

.tip-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    line-height: 1.3;
}

.tip-item p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    font-weight: 400;
}

/* Loading and Empty States */
.loading-state {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 4px solid rgba(11, 76, 122, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.empty-state {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.empty-state i {
    font-size: 4rem;
    color: rgba(11, 76, 122, 0.3);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* Hotel Info Modal */
.hotel-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    max-width: 600px;
    margin: 2rem auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid rgba(11, 76, 122, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: var(--spacing-xl);
}

/* Modern Responsive Design */
@media (max-width: 768px) {
    .hero-stats {
        gap: 1rem;
    }

    .stat-item {
        padding: 1rem 1.5rem;
        min-width: 120px;
    }

    .filter-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .filter-title h2 {
        font-size: var(--font-size-xl);
        flex-direction: column;
        text-align: center;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        padding: 2rem;
        gap: 1.5rem;
    }

    .filter-group {
        min-width: 100%;
    }

    .info-cards {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .info-card {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        padding: 1.5rem;
    }

    .hotels-count {
        font-size: var(--font-size-lg);
        justify-content: center;
    }

    .hotels-count::before {
        display: none;
    }

    .sort-options {
        justify-content: center;
        margin-top: 1rem;
    }

    .hotels-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hotel-card {
        margin: 0;
        transform: none !important;
    }

    .hotel-card:hover {
        transform: translateY(-8px) scale(1.02) !important;
    }

    .hotel-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .info-btn {
        width: 100%;
        height: auto;
        padding: 1rem;
        justify-content: center;
    }

    .tips-header .tips-title {
        font-size: var(--font-size-xl);
    }

    .tips-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .venue-info-card {
        flex-direction: column;
        text-align: center;
        padding: 2rem;
    }

    .venue-info-content {
        flex-direction: column;
        text-align: center;
    }

    .modal-content {
        margin: 1rem auto;
        border-radius: var(--radius-xl);
    }
}

@media (max-width: 480px) {
    .hotels-filter-section {
        padding: var(--spacing-lg) 0;
    }
    
    .hotels-grid-section {
        padding: var(--spacing-xl) 0;
    }
    
    .hotel-content {
        padding: var(--spacing-lg);
    }
    
    .booking-tips-section {
        padding: var(--spacing-xl) 0;
    }
    
    .tip-item {
        padding: var(--spacing-lg);
    }
}

/* Modern Animation for cards appearing */
.hotel-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: modernFadeInUp 0.8s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
}

.hotel-card:nth-child(1) { animation-delay: 0.1s; }
.hotel-card:nth-child(2) { animation-delay: 0.15s; }
.hotel-card:nth-child(3) { animation-delay: 0.2s; }
.hotel-card:nth-child(4) { animation-delay: 0.25s; }
.hotel-card:nth-child(5) { animation-delay: 0.3s; }
.hotel-card:nth-child(6) { animation-delay: 0.35s; }
.hotel-card:nth-child(7) { animation-delay: 0.4s; }
.hotel-card:nth-child(8) { animation-delay: 0.45s; }
.hotel-card:nth-child(9) { animation-delay: 0.5s; }
.hotel-card:nth-child(10) { animation-delay: 0.55s; }
.hotel-card:nth-child(11) { animation-delay: 0.6s; }
.hotel-card:nth-child(12) { animation-delay: 0.65s; }

@keyframes modernFadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Scroll-triggered animations */
@media (prefers-reduced-motion: no-preference) {
    .hotel-card {
        animation-play-state: paused;
    }

    .hotel-card.animate {
        animation-play-state: running;
    }
}

/* Enhanced hover effects for better interactivity */
.hotel-card:hover .hotel-content {
    transform: translateY(-5px);
}

.hotel-card:hover .hotel-features .feature-item {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.hotel-card:hover .hotel-features .feature-item:nth-child(1) { transition-delay: 0.1s; }
.hotel-card:hover .hotel-features .feature-item:nth-child(2) { transition-delay: 0.15s; }
.hotel-card:hover .hotel-features .feature-item:nth-child(3) { transition-delay: 0.2s; }
.hotel-card:hover .hotel-features .feature-item:nth-child(4) { transition-delay: 0.25s; }

/* Modern Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    box-shadow:
        0 8px 25px rgba(46, 89, 144, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow:
        0 15px 35px rgba(46, 89, 144, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

.scroll-to-top:active {
    transform: translateY(-3px) scale(1.05);
}

/* Modern Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(46, 89, 144, 0.1);
    z-index: 1001;
}

.scroll-progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.1s ease;
    border-radius: 0 2px 2px 0;
    box-shadow: 0 0 10px rgba(46, 89, 144, 0.3);
}