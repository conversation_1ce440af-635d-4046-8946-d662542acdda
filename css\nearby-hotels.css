/* Nearby Hotels Page Styles */

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    text-align: center;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(11, 76, 122, 0.1);
    border-left: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    font-weight: 500;
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3xl);
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius-xl);
    border: 2px dashed rgba(11, 76, 122, 0.2);
}

.empty-state i {
    font-size: 4rem;
    color: rgba(11, 76, 122, 0.3);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* Filter Header */
.filter-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.filter-title h2 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.filter-title p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    max-width: 600px;
    margin: 0 auto;
}

/* Hotels Filter Section */
.hotels-filter-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
    border-bottom: 1px solid rgba(11, 76, 122, 0.1);
    position: relative;
}

.hotels-filter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.4' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.hotels-filter-section .container {
    position: relative;
    z-index: 1;
}

.filter-controls {
    display: flex;
    gap: var(--spacing-xl);
    align-items: end;
    flex-wrap: wrap;
    justify-content: center;
    max-width: 900px;
    margin: 0 auto;
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    box-shadow: 0 15px 35px rgba(11, 76, 122, 0.1);
    border: 1px solid rgba(11, 76, 122, 0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    min-width: 220px;
    flex: 1;
}

.filter-group label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.filter-select {
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid rgba(11, 76, 122, 0.15);
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, #fafcff 0%, #f0f8ff 100%);
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-primary);
    transition: all 0.3s ease;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-md) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: calc(var(--spacing-lg) + 2em);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(11, 76, 122, 0.1);
    background: white;
}

.filter-select:hover {
    border-color: var(--primary-color);
    background: white;
}

.reset-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-height: 52px;
    white-space: nowrap;
}

.reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

/* Section Header Styles */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.hotels-count {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.hotels-count span {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-weight: 800;
    min-width: 40px;
    text-align: center;
}

.sort-options {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid rgba(11, 76, 122, 0.15);
    border-radius: var(--border-radius-md);
    background: white;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Hotels Grid Section */
.hotels-grid-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
}

.hotels-grid-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.1'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4z'/%3E%3Cpath d='m6 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.hotels-grid-section .container {
    position: relative;
    z-index: 1;
}

.hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Hotel Card Styles */
.hotel-card {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.08);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 1px solid rgba(11, 76, 122, 0.1);
    position: relative;
    backdrop-filter: blur(10px);
}

.hotel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: 1;
}

.hotel-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 50px rgba(11, 76, 122, 0.2);
}

.hotel-image-container {
    position: relative;
    height: 240px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(11, 76, 122, 0.1) 0%, rgba(30, 136, 229, 0.1) 100%);
}

.hotel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.hotel-card:hover .hotel-image {
    transform: scale(1.05);
}

.hotel-category-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: rgba(11, 76, 122, 0.95);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 700;
    backdrop-filter: blur(15px);
    z-index: 2;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hotel-category-badge.luxury {
    background: linear-gradient(135deg, #ffd700 0%, #f39c12 100%);
    color: #1a1a1a;
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
}

.hotel-category-badge.business {
    background: linear-gradient(135deg, #1e88e5 0%, #3f51b5 100%);
    border-color: rgba(30, 136, 229, 0.5);
    box-shadow: 0 4px 20px rgba(30, 136, 229, 0.4);
}

.hotel-category-badge.comfort {
    background: linear-gradient(135deg, #26a69a 0%, #009688 100%);
    border-color: rgba(38, 166, 154, 0.5);
    box-shadow: 0 4px 20px rgba(38, 166, 154, 0.4);
}

.hotel-content {
    padding: var(--spacing-xl);
}

.hotel-header {
    margin-bottom: var(--spacing-lg);
}

.hotel-name {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

.hotel-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    color: #ffd700;
    font-size: var(--font-size-sm);
}

.rating-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.hotel-distance {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(11, 76, 122, 0.05);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);
}

.distance-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.distance-primary {
    font-weight: 600;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.distance-secondary {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.hotel-features {
    margin-bottom: var(--spacing-lg);
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: rgba(30, 136, 229, 0.1);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.feature-icon {
    color: var(--secondary-color);
    font-size: var(--font-size-xs);
}

.hotel-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.book-btn {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-decoration: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.book-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(11, 76, 122, 0.3);
    text-decoration: none;
    color: white;
}

.info-btn {
    background: white;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: var(--font-size-lg);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(11, 76, 122, 0.2);
}

/* Venue Info Section */
.venue-info-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
}

.venue-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.venue-info-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.venue-info-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 1;
}

.venue-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    box-shadow: 0 8px 25px rgba(11, 76, 122, 0.3);
}

.venue-details h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.venue-details p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.venue-details i {
    color: var(--secondary-color);
    width: 20px;
}

.venue-actions .venue-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-decoration: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.venue-actions .venue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(11, 76, 122, 0.3);
    text-decoration: none;
    color: white;
}

/* Booking Tips Section */
.booking-tips-section {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.booking-tips-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0f2fe' fill-opacity='0.4'%3E%3Cpath d='M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2L74 40h-2zm4 0l4-4v2L78 40h-2z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
}

.booking-tips-section .container {
    position: relative;
    z-index: 1;
}

.tips-content {
    max-width: 1000px;
    margin: 0 auto;
}

.tips-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.tips-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 50%;
    margin-bottom: var(--spacing-lg);
    color: white;
    font-size: var(--font-size-2xl);
    box-shadow: 0 15px 35px rgba(251, 191, 36, 0.3);
    position: relative;
}

.tips-icon::before {
    content: '';
    position: absolute;
    inset: -4px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b, #fbbf24);
    border-radius: 50%;
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 0.3; }
}

.tips-title {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.tips-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-xl);
}

.tip-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-xl);
    text-align: center;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.08);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.tip-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color) 0%, var(--primary-color) 100%);
}

.tip-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(11, 76, 122, 0.15);
    border-color: rgba(30, 136, 229, 0.3);
}

.tip-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    color: white;
    font-size: var(--font-size-xl);
    box-shadow: 0 8px 25px rgba(30, 136, 229, 0.3);
    position: relative;
}

.tip-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border-radius: 50%;
    z-index: -1;
    opacity: 0.3;
}

.tip-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    line-height: 1.3;
}

.tip-item p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.7;
    font-weight: 400;
}

/* Loading and Empty States */
.loading-state {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.loading-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 4px solid rgba(11, 76, 122, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.empty-state {
    text-align: center;
    padding: var(--spacing-3xl) 0;
}

.empty-state i {
    font-size: 4rem;
    color: rgba(11, 76, 122, 0.3);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

/* Hotel Info Modal */
.hotel-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    max-width: 600px;
    margin: 2rem auto;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid rgba(11, 76, 122, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: var(--spacing-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-header {
        text-align: left;
        margin-bottom: var(--spacing-lg);
    }
    
    .filter-title h2 {
        font-size: var(--font-size-lg);
        flex-direction: column;
        text-align: center;
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        padding: var(--spacing-lg);
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .hotels-count {
        font-size: var(--font-size-base);
        justify-content: center;
    }
    
    .sort-options {
        justify-content: center;
        margin-top: var(--spacing-md);
    }
    
    .hotels-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .hotel-card {
        margin: 0;
    }
    
    .hotel-actions {
        flex-direction: column;
    }
    
    .info-btn {
        width: 100%;
        height: auto;
        padding: var(--spacing-md);
    }
    
    .tips-header .tips-title {
        font-size: var(--font-size-xl);
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .venue-info-card {
        flex-direction: column;
        text-align: center;
    }
    
    .venue-info-content {
        flex-direction: column;
        text-align: center;
    }
    
    .modal-content {
        margin: 1rem auto;
    }
}

@media (max-width: 480px) {
    .hotels-filter-section {
        padding: var(--spacing-lg) 0;
    }
    
    .hotels-grid-section {
        padding: var(--spacing-xl) 0;
    }
    
    .hotel-content {
        padding: var(--spacing-lg);
    }
    
    .booking-tips-section {
        padding: var(--spacing-xl) 0;
    }
    
    .tip-item {
        padding: var(--spacing-lg);
    }
}

/* Animation for cards appearing */
.hotel-card {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.hotel-card:nth-child(1) { animation-delay: 0.1s; }
.hotel-card:nth-child(2) { animation-delay: 0.2s; }
.hotel-card:nth-child(3) { animation-delay: 0.3s; }
.hotel-card:nth-child(4) { animation-delay: 0.4s; }
.hotel-card:nth-child(5) { animation-delay: 0.5s; }
.hotel-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 