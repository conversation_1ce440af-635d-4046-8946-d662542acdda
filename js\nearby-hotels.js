// Nearby Hotels JavaScript
document.addEventListener('DOMContentLoaded', function() {
    
    // Hotel data based on the provided URLs and real information
    const hotelData = [
        {
            name: "Holiday Inn Express Ahmedabad Prahlad Nagar",
            category: "business",
            rating: 4.2,
            distanceFromClub: "5.8 km (~15-20 min)",
            distanceFromAirport: "17-20 km (~30-45 min)",
            image: "assets/hotels/Holiday Inn Express – Prahlad Nagar.webp",
            features: ["Free WiFi", "Free Breakfast", "Fitness Center", "24/7 Front Desk", "Business Center"],
            bookingUrl: "https://www.ihg.com/holidayinnexpress/hotels/us/en/gujarat/gjupn/hoteldetail",
            phone: "+91-79-4900-4141",
            address: "Plot No.38/1, Prahlad Nagar, Ahmedabad - 380015",
            description: "Modern business hotel with complimentary breakfast and excellent amenities for conference attendees."
        },
        {
            name: "Crowne Plaza Ahmedabad City Centre",
            category: "luxury",
            rating: 4.5,
            distanceFromClub: "5.17 km (~12-15 min)",
            distanceFromAirport: "14 km (~30-45 min)",
            image: "assets/hotels/Crowne Plaza Ahmedabad City Centre jpg.webp",
            features: ["Luxury Spa", "Multiple Restaurants", "Pool", "Concierge", "Business Lounge"],
            bookingUrl: "https://www.ihg.com/crowneplaza/hotels/us/en/ahmedabad/amdch/hoteldetail",
            phone: "+91-79-6671-1234",
            address: "City Centre, Ahmedabad",
            description: "Upscale hotel in the heart of Ahmedabad with premium amenities and world-class service."
        },
        {
            name: "Taj Skyline Ahmedabad",
            category: "luxury",
            rating: 4.4,
            distanceFromClub: "5-6 km (~12 min)",
            distanceFromAirport: "17-18 km (~35-40 min)",
            image: "assets/hotels/Taj Skyline Ahmedabad.webp",
            features: ["Taj Hospitality", "Fine Dining", "Spa & Wellness", "Business Center", "Valet Parking"],
            bookingUrl: "https://www.tajhotels.com/en-in/hotels/taj-skyline-ahmedabad",
            phone: "+91-79-6666-3939",
            address: "Sankalp Square, Ahmedabad",
            description: "Iconic Taj hospitality with luxurious accommodations and exceptional dining experiences."
        },
        {
            name: "Novotel Ahmedabad",
            category: "business",
            rating: 4.3,
            distanceFromClub: "5.75 km (~15 min)",
            distanceFromAirport: "18 km (~35-45 min)",
            image: "assets/hotels/Novotel Ahmedabad.webp",
            features: ["Modern Rooms", "Pool", "Fitness Center", "Business Facilities", "Multiple Dining"],
            bookingUrl: "https://all.accor.com/hotel/8173/index.en.shtml",
            phone: "+91-79-4040-1234",
            address: "Ashram Road, Ahmedabad",
            description: "Contemporary Accor hotel offering modern amenities and convenient location for business travelers."
        },
        {
            name: "Ramada by Wyndham Ahmedabad",
            category: "business",
            rating: 4.1,
            distanceFromClub: "5.18 km (~12 min)",
            distanceFromAirport: "8-10 km (~20-30 min)",
            image: "assets/hotels/Ramada by Wyndham (Planet Landmark).webp",
            features: ["Business Center", "Restaurant", "Free WiFi", "Conference Rooms", "Airport Shuttle"],
            bookingUrl: "https://www.makemytrip.com/hotels/hotel-details/?hotelId=201010301812539460",
            phone: "+91-79-4040-7777",
            address: "Sindhu Bhavan Road, Ahmedabad",
            description: "Well-appointed business hotel with excellent connectivity and professional amenities."
        },
        {
            name: "Ginger Ahmedabad",
            category: "comfort",
            rating: 4.0,
            distanceFromClub: "6-7 km (~15 min)",
            distanceFromAirport: "15-20 km (~30-40 min)",
            image: "assets/hotels/Ginger – SBR  Satellite.webp",
            features: ["Lean Luxe Rooms", "Cafe", "Business Center", "Laundry Service", "24/7 Security"],
            bookingUrl: "https://www.gingerhotels.com/destinations/hotels-in-ahmedabad",
            phone: "+91-79-4605-4605",
            address: "Satellite Road, Ahmedabad",
            description: "Smart, efficient hotel offering essential amenities with great value for money."
        },
        {
            name: "Pride Plaza Hotel Ahmedabad",
            category: "business",
            rating: 4.2,
            distanceFromClub: "7.09 km (~15-18 min)",
            distanceFromAirport: "16-18 km (~35 min)",
            image: "assets/hotels/Pride Plaza Hotel.webp",
            features: ["Business Hotel", "Restaurant", "Meeting Rooms", "Parking", "Room Service"],
            bookingUrl: "https://www.pridehotel.com/pride-plaza-hotel-ahmedabad/",
            phone: "+91-79-4000-5555",
            address: "S.G. Highway, Ahmedabad",
            description: "Professional business hotel with modern facilities and strategic location."
        },
        {
            name: "ITC Narmada Ahmedabad",
            category: "luxury",
            rating: 4.6,
            distanceFromClub: "8.15 km (~20 min)",
            distanceFromAirport: "12-15 km (~30-40 min)",
            image: "assets/hotels/ITC Narmada.webp",
            features: ["ITC Luxury", "Spa", "Multiple Restaurants", "Pool", "Butler Service"],
            bookingUrl: "https://www.itchotels.com/in/en/itcnarmada-ahmedabad",
            phone: "+91-79-2358-5585",
            address: "Keshavbaug, Ahmedabad",
            description: "Luxury ITC property offering world-class amenities and impeccable hospitality."
        },
        {
            name: "Hyatt Ahmedabad",
            category: "luxury",
            rating: 4.4,
            distanceFromClub: "12.55 km (~25 min)",
            distanceFromAirport: "15-18 km (~35 min)",
            image: "assets/hotels/Hyatt Vastrapur.webp",
            features: ["Hyatt Service", "Pool", "Spa", "Fine Dining", "Business Center"],
            bookingUrl: "https://www.hyatt.com/en-US/hotel/india/hyatt-ahmedabad/amdhy",
            phone: "+91-79-4925-1234",
            address: "Vastrapur, Ahmedabad",
            description: "Premium Hyatt hotel offering sophisticated accommodations and exceptional amenities."
        },
        {
            name: "Renaissance Ahmedabad Hotel",
            category: "luxury",
            rating: 4.3,
            distanceFromClub: "10.63 km (~20 min)",
            distanceFromAirport: "16-18 km (~35 min)",
            image: "assets/hotels/Renaissance Ahmedabad.webp",
            features: ["Marriott Brand", "Pool", "Spa", "Restaurant", "Event Spaces"],
            bookingUrl: "https://www.marriott.com/en-us/hotels/amdbr-renaissance-ahmedabad-hotel/overview/",
            phone: "+91-79-6671-1234",
            address: "Ramdev Nagar, Ahmedabad",
            description: "Upscale Marriott Renaissance hotel with contemporary design and premium facilities."
        },
        {
            name: "Courtyard by Marriott Ahmedabad",
            category: "business",
            rating: 4.2,
            distanceFromClub: "9-11 km (~20 min)",
            distanceFromAirport: "17 km (~35-40 min)",
            image: "assets/hotels/Courtyard by Marriott Sindhu Bhavan.webp",
            features: ["Business Hotel", "Pool", "Fitness Center", "Restaurant", "Meeting Rooms"],
            bookingUrl: "https://www.marriott.com/en-us/hotels/amdsb-courtyard-ahmedabad-sindhu-bhavan-road/overview/",
            phone: "+91-79-4025-1234",
            address: "Sindhu Bhavan Road, Ahmedabad",
            description: "Modern Courtyard hotel designed for business travelers with efficient amenities."
        },
        {
            name: "Vivanta Ahmedabad",
            category: "luxury",
            rating: 4.5,
            distanceFromClub: "5-6 km (~12 min)",
            distanceFromAirport: "17 km (~35-40 min)",
            image: "assets/hotels/Taj Vivanta.webp",
            features: ["Tata Hospitality", "Spa", "Fine Dining", "Pool", "Concierge"],
            bookingUrl: "https://www.vivantahotels.com/en-in",
            phone: "+91-79-4028-1234",
            address: "S.G. Highway, Ahmedabad",
            description: "Stylish Vivanta hotel combining contemporary luxury with warm Indian hospitality."
        }
    ];

    // DOM elements
    const hotelsGrid = document.getElementById('hotelsGrid');
    const categoryFilter = document.getElementById('categoryFilter');
    const distanceFilter = document.getElementById('distanceFilter');
    const resetFilters = document.getElementById('resetFilters');
    const hotelsCount = document.getElementById('hotelsCount');
    const sortSelect = document.getElementById('sortHotels');

    let filteredHotels = [...hotelData];

    // Initialize page
    function init() {
        renderHotels(hotelData);
        setupEventListeners();
        updateHotelsCount(hotelData.length);
    }

    // Setup event listeners
    function setupEventListeners() {
        if (categoryFilter) categoryFilter.addEventListener('change', applyFilters);
        if (distanceFilter) distanceFilter.addEventListener('change', applyFilters);
        if (resetFilters) resetFilters.addEventListener('click', resetAllFilters);
        if (sortSelect) sortSelect.addEventListener('change', sortHotels);
    }

    // Render hotels to the grid
    function renderHotels(hotels) {
        if (hotels.length === 0) {
            showEmptyState();
            return;
        }

        hotelsGrid.innerHTML = hotels.map((hotel, index) => createHotelCard(hotel, index)).join('');
        
        // Add click event listeners to info buttons
        document.querySelectorAll('.info-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const hotelName = e.target.closest('.hotel-card').dataset.hotelName;
                const hotel = hotelData.find(h => h.name === hotelName);
                showHotelModal(hotel);
            });
        });
    }

    // Create individual hotel card
    function createHotelCard(hotel, index) {
        const starsHtml = generateStars(hotel.rating);
        
        return `
            <div class="hotel-card" data-hotel-name="${hotel.name}" style="animation-delay: ${index * 0.1}s">
                <div class="hotel-image-container">
                    <img src="${hotel.image}" alt="${hotel.name}" class="hotel-image" loading="lazy">
                    <div class="hotel-category-badge ${hotel.category}">
                        ${hotel.category === 'luxury' ? '5 Star Luxury' : 
                          hotel.category === 'business' ? '4 Star Business' : '3 Star Comfort'}
                    </div>
                </div>
                <div class="hotel-content">
                    <div class="hotel-header">
                        <h3 class="hotel-name">${hotel.name}</h3>
                        <div class="hotel-rating">
                            <div class="stars">${starsHtml}</div>
                            <span class="rating-text">${hotel.rating}/5</span>
                        </div>
                    </div>
                    
                    <div class="hotel-distance">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="distance-info">
                            <span class="distance-primary">From Club 07: ${hotel.distanceFromClub}</span>
                            <span class="distance-secondary">From Airport: ${hotel.distanceFromAirport}</span>
                        </div>
                    </div>
                    
                    <div class="hotel-features">
                        <ul class="features-list">
                            ${hotel.features.slice(0, 4).map(feature => `
                                <li class="feature-item">
                                    <i class="fas fa-check feature-icon"></i>
                                    ${feature}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    
                    <div class="hotel-actions">
                        <a href="${hotel.bookingUrl}" target="_blank" class="book-btn">
                            <i class="fas fa-calendar-check"></i>
                            Book Now
                        </a>
                        <button class="info-btn" title="More Information">
                            <i class="fas fa-info"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Generate star rating HTML
    function generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        let starsHtml = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star star"></i>';
        }
        
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt star"></i>';
        }
        
        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star star"></i>';
        }
        
        return starsHtml;
    }

    // Apply filters
    function applyFilters() {
        const categoryValue = categoryFilter.value;
        const distanceValue = distanceFilter.value;
        
        filteredHotels = hotelData.filter(hotel => {
            // Category filter
            if (categoryValue !== 'all' && hotel.category !== categoryValue) {
                return false;
            }
            
            // Distance filter (approximate based on km)
            if (distanceValue !== 'all') {
                const distanceKm = parseFloat(hotel.distanceFromClub.match(/[\d.]+/)[0]);
                if (distanceValue === 'near' && distanceKm > 10) return false;
                if (distanceValue === 'medium' && (distanceKm <= 10 || distanceKm > 15)) return false;
                if (distanceValue === 'far' && distanceKm <= 15) return false;
            }
            
            return true;
        });
        
        renderHotels(filteredHotels);
        updateHotelsCount(filteredHotels.length);
    }

    // Sort hotels
    function sortHotels() {
        if (!sortSelect) return;
        
        const sortValue = sortSelect.value;
        
        filteredHotels.sort((a, b) => {
            switch (sortValue) {
                case 'rating':
                    return b.rating - a.rating;
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'distance':
                default:
                    const distanceA = parseFloat(a.distanceFromClub.match(/[\d.]+/)[0]);
                    const distanceB = parseFloat(b.distanceFromClub.match(/[\d.]+/)[0]);
                    return distanceA - distanceB;
            }
        });
        
        renderHotels(filteredHotels);
    }

    // Reset all filters
    function resetAllFilters() {
        if (categoryFilter) categoryFilter.value = 'all';
        if (distanceFilter) distanceFilter.value = 'all';
        if (sortSelect) sortSelect.value = 'distance';
        filteredHotels = [...hotelData];
        renderHotels(filteredHotels);
        updateHotelsCount(filteredHotels.length);
    }

    // Update hotels count
    function updateHotelsCount(count) {
        if (hotelsCount) {
            // Animate count change
            const currentCount = parseInt(hotelsCount.textContent) || 0;
            const targetCount = count;
            
            if (currentCount !== targetCount) {
                let current = currentCount;
                const increment = targetCount > currentCount ? 1 : -1;
                const duration = Math.abs(targetCount - currentCount) * 50; // 50ms per number
                
                const timer = setInterval(() => {
                    current += increment;
                    hotelsCount.textContent = current;
                    
                    if (current === targetCount) {
                        clearInterval(timer);
                    }
                }, duration / Math.abs(targetCount - currentCount));
            }
        }
    }

    // Show empty state
    function showEmptyState() {
        if (!hotelsGrid) return;
        
        hotelsGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-hotel"></i>
                <h3>No hotels found</h3>
                <p>Try adjusting your filters to see more results.</p>
            </div>
        `;
    }

    // Show hotel information modal
    function showHotelModal(hotel) {
        const modal = document.createElement('div');
        modal.className = 'hotel-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${hotel.name}</h3>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="hotel-info">
                        <p><strong>Description:</strong> ${hotel.description}</p>
                        <p><strong>Address:</strong> ${hotel.address}</p>
                        <p><strong>Phone:</strong> <a href="tel:${hotel.phone}">${hotel.phone}</a></p>
                        <p><strong>Rating:</strong> ${hotel.rating}/5 (${hotel.category === 'luxury' ? '5 Star' : hotel.category === 'business' ? '4 Star' : '3 Star'})</p>
                        
                        <h4>Distance Information:</h4>
                        <ul>
                            <li>From Club 07: ${hotel.distanceFromClub}</li>
                            <li>From Airport: ${hotel.distanceFromAirport}</li>
                        </ul>
                        
                        <h4>Amenities:</h4>
                        <ul>
                            ${hotel.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                        
                        <div style="margin-top: 20px;">
                            <a href="${hotel.bookingUrl}" target="_blank" class="book-btn">
                                <i class="fas fa-external-link-alt"></i>
                                Visit Hotel Website
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        modal.style.display = 'block';
        
        // Close modal events
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => {
            modal.remove();
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // Close with Escape key
        document.addEventListener('keydown', function escapeHandler(e) {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', escapeHandler);
            }
        });
    }

    // Modern scroll to top functionality
    function initScrollToTop() {
        const scrollToTopBtn = document.getElementById('scrollToTop');
        const progressBar = document.querySelector('.scroll-progress-bar');

        if (!scrollToTopBtn || !progressBar) return;

        // Show/hide scroll to top button and update progress bar
        function updateScrollElements() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / scrollHeight) * 100;

            // Update progress bar
            progressBar.style.width = scrollPercent + '%';

            // Show/hide scroll to top button
            if (scrollTop > 300) {
                scrollToTopBtn.classList.add('visible');
            } else {
                scrollToTopBtn.classList.remove('visible');
            }
        }

        // Smooth scroll to top
        scrollToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Update on scroll
        window.addEventListener('scroll', updateScrollElements);

        // Initial update
        updateScrollElements();
    }

    // Intersection Observer for card animations
    function initScrollAnimations() {
        const cards = document.querySelectorAll('.hotel-card');

        if (!cards.length) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        cards.forEach(card => {
            observer.observe(card);
        });
    }

    // Enhanced loading state with better UX
    function showLoadingState() {
        if (!hotelsGrid) return;

        hotelsGrid.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>Loading premium hotels...</p>
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
    }

    // Initialize all modern features
    function initModernFeatures() {
        initScrollToTop();
        initScrollAnimations();

        // Add smooth scrolling to all internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading state when filters change
        const originalApplyFilters = applyFilters;
        applyFilters = function() {
            showLoadingState();
            setTimeout(() => {
                originalApplyFilters();
                initScrollAnimations();
            }, 300);
        };
    }

    // Initialize the page with modern features
    init();
    initModernFeatures();
});