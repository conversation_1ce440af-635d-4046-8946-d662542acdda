/* Common styles for logo links and footer styling */

/* Remove text decoration from all logo links */
.logo-link,
.mobile-logo-link,
.footer-logo-link,
.message-logo-link,
.seal-logo-link {
    text-decoration: none !important;
    color: inherit;
}

/* Ensure no text decoration on hover */
.logo-link:hover,
.logo-link:focus,
.mobile-logo-link:hover,
.mobile-logo-link:focus,
.footer-logo-link:hover,
.footer-logo-link:focus,
.message-logo-link:hover,
.message-logo-link:focus,
.seal-logo-link:hover,
.seal-logo-link:focus {
    text-decoration: none !important;
    color: inherit;
}

/* Maintain original colors and styles for nested elements */
.logo-link * {
    color: inherit;
}

.mobile-logo-link * {
    color: inherit;
}

.footer-logo-link * {
    color: inherit;
}

.message-logo-link * {
    color: inherit;
}

.seal-logo-link * {
    color: inherit;
}

/* Footer text styling - Make all footer text white */
.footer-section,
.footer-section * {
    color: white !important;
}

/* Ensure footer links remain white and remove underlines */
.footer-section a {
    color: white !important;
    text-decoration: none !important;
}

.footer-section a:hover,
.footer-section a:focus {
    color: #f0f0f0 !important;
    text-decoration: none !important;
}

/* Specific footer elements */
.footer-conference-title,
.footer-conference-subtitle,
.footer-conference-date,
.footer-description,
.footer-column-title,
.footer-link,
.footer-copyright,
.contact-detail,
.organiser-name,
.organiser-email,
.organiser-phone,
.legal-link,
.developer-link {
    color: white !important;
}

/* Footer logo size increase */
.footer-logo {
    width: 80px !important;
    height: 80px !important;
    object-fit: contain;
}

/* Adjust footer logo container for better spacing */
.footer-logo-container {
    align-items: center;
    gap: 15px;
}

/* Multicolor Logo Text in Navigation */
.conference-title {
    background: linear-gradient(45deg, #0B4C7A, #1E88E5, #43A047, #FF9800, #E53935);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    font-weight: 700;
}

.conference-year {
    background: linear-gradient(45deg, #FF9800, #E53935, #9C27B0, #3F51B5);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 2s ease-in-out infinite reverse;
    font-weight: 600;
}

/* Multicolor animation */
@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Mobile logo multicolor */
.mobile-logo-link .logo-text h2 {
    background: linear-gradient(45deg, #0B4C7A, #1E88E5, #43A047, #FF9800, #E53935);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    font-weight: 700;
} 
.title-word {
    display: inline-block;
    margin: 0 1rem;
    word-break: keep-all;
    overflow-wrap: break-word;
}

.word-1 {
    background: linear-gradient(135deg, #FF8C42 0%, #FFB366 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.word-2 {
    background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
          background-clip: text;
  }