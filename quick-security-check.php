<?php
// ====================================================
// QUICK DAILY SECURITY CHECK - IMA NATCON 2025
// ====================================================
// Simple script for daily security monitoring

// Configuration
$secret_key = "natcon2025_security_key"; // Same as security-monitor.php
$admin_ip = "127.0.0.1";

// Security check
if (!isset($_GET['key']) || $_GET['key'] !== $secret_key) {
    die('Access denied');
}

$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
if ($client_ip !== $admin_ip && $client_ip !== '::1') {
    die('Access denied from IP: ' . $client_ip);
}

// Get security statistics
function getSecurityStats() {
    $stats = [];
    
    // Contact logs
    if (file_exists('contact_logs.txt')) {
        $logs = file('contact_logs.txt', FILE_IGNORE_NEW_LINES);
        $stats['total_logs'] = count($logs);
        
        // Count today's logs
        $today = date('Y-m-d');
        $today_logs = 0;
        $security_events = 0;
        
        foreach ($logs as $log) {
            if (strpos($log, $today) !== false) {
                $today_logs++;
            }
            if (strpos($log, 'SECURITY:') !== false) {
                $security_events++;
            }
        }
        
        $stats['today_logs'] = $today_logs;
        $stats['security_events'] = $security_events;
    }
    
    // Blacklisted IPs
    if (file_exists('blacklisted_ips.txt')) {
        $blacklisted = file('blacklisted_ips.txt', FILE_IGNORE_NEW_LINES);
        $stats['blacklisted_ips'] = count($blacklisted);
    } else {
        $stats['blacklisted_ips'] = 0;
    }
    
    // Rate limited IPs
    $rate_limit_files = glob("rate_limit_*.txt");
    $stats['rate_limited_ips'] = count($rate_limit_files);
    
    return $stats;
}

$stats = getSecurityStats();

// Get recent security events (last 10)
function getRecentSecurityEvents($count = 10) {
    if (!file_exists('contact_logs.txt')) {
        return [];
    }
    
    $logs = file('contact_logs.txt', FILE_IGNORE_NEW_LINES);
    $security_events = [];
    
    foreach ($logs as $log) {
        if (strpos($log, 'SECURITY:') !== false) {
            $security_events[] = $log;
        }
    }
    
    return array_slice($security_events, -$count);
}

$recent_events = getRecentSecurityEvents(10);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Security Check - IMA NATCON 2025</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .status-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .status-label {
            color: #6c757d;
            font-size: 12px;
        }
        .alert {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .alert.danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .events {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .event-item {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
            font-size: 12px;
            font-family: monospace;
        }
        .event-item:last-child {
            border-bottom: none;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Quick Security Check</h1>
            <p>IMA NATCON 2025 - <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
        
        <a href="?key=<?php echo htmlspecialchars($secret_key); ?>" class="refresh-btn">🔄 Refresh</a>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-number"><?php echo $stats['total_logs']; ?></div>
                <div class="status-label">Total Log Entries</div>
            </div>
            <div class="status-card">
                <div class="status-number"><?php echo $stats['today_logs']; ?></div>
                <div class="status-label">Today's Logs</div>
            </div>
            <div class="status-card">
                <div class="status-number"><?php echo $stats['security_events']; ?></div>
                <div class="status-label">Security Events</div>
            </div>
            <div class="status-card">
                <div class="status-number"><?php echo $stats['blacklisted_ips']; ?></div>
                <div class="status-label">Blacklisted IPs</div>
            </div>
            <div class="status-card">
                <div class="status-number"><?php echo $stats['rate_limited_ips']; ?></div>
                <div class="status-label">Rate Limited IPs</div>
            </div>
        </div>

        <?php if ($stats['today_logs'] > 0): ?>
            <div class="alert warning">
                ⚠️ <?php echo $stats['today_logs']; ?> activities logged today
            </div>
        <?php else: ?>
            <div class="alert success">
                ✅ No activities logged today - All clear!
            </div>
        <?php endif; ?>

        <?php if ($stats['security_events'] > 0): ?>
            <div class="alert danger">
                🚨 <?php echo $stats['security_events']; ?> security events detected
            </div>
        <?php endif; ?>

        <?php if ($stats['blacklisted_ips'] > 0): ?>
            <div class="alert warning">
                🚫 <?php echo $stats['blacklisted_ips']; ?> IPs are blacklisted
            </div>
        <?php endif; ?>

        <div class="events">
            <h3>🔍 Recent Security Events (Last 10)</h3>
            <?php if (empty($recent_events)): ?>
                <p>No security events found.</p>
            <?php else: ?>
                <?php foreach ($recent_events as $event): ?>
                    <div class="event-item"><?php echo htmlspecialchars($event); ?></div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div class="alert success">
            <strong>✅ Security Status:</strong> All systems operational
        </div>

        <p><small>Last checked: <?php echo date('Y-m-d H:i:s'); ?></small></p>
    </div>
</body>
</html> 