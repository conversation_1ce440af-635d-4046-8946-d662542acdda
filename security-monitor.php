<?php
// ====================================================
// SECURITY MONITORING SCRIPT FOR IMA NATCON 2025
// ====================================================
// This script provides a secure way to monitor security logs
// Access this file with: ?key=YOUR_SECRET_KEY

// ========== CONFIGURATION ==========
$secret_key = "natcon2025_security_key"; // Change this to a strong secret key
$admin_ip = "127.0.0.1"; // Your IP address for admin access
// ========== END CONFIGURATION ==========

// Security check
if (!isset($_GET['key']) || $_GET['key'] !== $secret_key) {
    die('Access denied');
}

// IP check for additional security
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
if ($client_ip !== $admin_ip && $client_ip !== '::1') {
    die('Access denied from this IP: ' . $client_ip);
}

// Function to read log files safely
function readLogFile($filename, $lines = 50) {
    if (!file_exists($filename)) {
        return "File not found: $filename";
    }
    
    $content = file($filename, FILE_IGNORE_NEW_LINES);
    if ($content === false) {
        return "Error reading file: $filename";
    }
    
    // Get last N lines
    $content = array_slice($content, -$lines);
    return implode("\n", $content);
}

// Function to get file size in human readable format
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Function to get blacklisted IPs
function getBlacklistedIPs() {
    $blacklist_file = "blacklisted_ips.txt";
    if (!file_exists($blacklist_file)) {
        return "No blacklisted IPs found";
    }
    
    $ips = file($blacklist_file, FILE_IGNORE_NEW_LINES);
    if (empty($ips)) {
        return "No blacklisted IPs found";
    }
    
    return implode(", ", $ips);
}

// Function to get rate limit files
function getRateLimitFiles() {
    $files = glob("rate_limit_*.txt");
    if (empty($files)) {
        return "No rate limit files found";
    }
    
    $result = [];
    foreach ($files as $file) {
        $ip_hash = str_replace(['rate_limit_', '.txt'], '', $file);
        $size = formatFileSize(filesize($file));
        $result[] = "Hash: $ip_hash, Size: $size";
    }
    
    return implode("\n", $result);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Monitor - IMA NATCON 2025</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #007bff;
            margin-top: 0;
        }
        .log-content {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Security Monitor - IMA NATCON 2025</h1>
        
        <a href="?key=<?php echo htmlspecialchars($secret_key); ?>" class="refresh-btn">🔄 Refresh</a>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo file_exists('contact_logs.txt') ? count(file('contact_logs.txt')) : 0; ?></div>
                <div class="stat-label">Total Log Entries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo file_exists('blacklisted_ips.txt') ? count(file('blacklisted_ips.txt')) : 0; ?></div>
                <div class="stat-label">Blacklisted IPs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count(glob("rate_limit_*.txt")); ?></div>
                <div class="stat-label">Rate Limited IPs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $client_ip; ?></div>
                <div class="stat-label">Your IP Address</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Security Status</h2>
            <div class="status success">
                ✅ Security monitoring is active
            </div>
            <div class="status success">
                ✅ Rate limiting is enabled (5 requests/hour per IP)
            </div>
            <div class="status success">
                ✅ Input sanitization is active
            </div>
            <div class="status success">
                ✅ Suspicious content detection is active
            </div>
        </div>

        <div class="section">
            <h2>📝 Recent Security Logs (Last 50 entries)</h2>
            <div class="log-content"><?php echo htmlspecialchars(readLogFile('contact_logs.txt', 50)); ?></div>
        </div>

        <div class="section">
            <h2>🚫 Blacklisted IP Addresses</h2>
            <div class="log-content"><?php echo htmlspecialchars(getBlacklistedIPs()); ?></div>
        </div>

        <div class="section">
            <h2>⏱️ Rate Limit Files</h2>
            <div class="log-content"><?php echo htmlspecialchars(getRateLimitFiles()); ?></div>
        </div>

        <div class="section">
            <h2>📁 File Information</h2>
            <div class="log-content">
Contact Logs: <?php echo file_exists('contact_logs.txt') ? formatFileSize(filesize('contact_logs.txt')) : 'Not found'; ?>
Blacklist File: <?php echo file_exists('blacklisted_ips.txt') ? formatFileSize(filesize('blacklisted_ips.txt')) : 'Not found'; ?>
Rate Limit Files: <?php echo count(glob("rate_limit_*.txt")); ?> files found
            </div>
        </div>

        <div class="section">
            <h2>🔧 Security Actions</h2>
            <p><strong>To clear logs:</strong> Delete the log files manually from server</p>
            <p><strong>To unblacklist IP:</strong> Remove IP from blacklisted_ips.txt file</p>
            <p><strong>To reset rate limits:</strong> Delete rate_limit_*.txt files</p>
            <p><strong>Last updated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html> 