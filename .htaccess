RewriteEngine On

# ====================================================
# SECURITY HEADERS AND PROTECTIONS FOR IMA NATCON 2025
# ====================================================

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (CSP)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Protect sensitive files
<Files "contact_logs.txt">
    Order allow,deny
    Deny from all
</Files>

<Files "blacklisted_ips.txt">
    Order allow,deny
    Deny from all
</Files>

<Files "rate_limit_*.txt">
    Order allow,deny
    Deny from all
</Files>

# Protect PHP files from direct access (except index.php)
<FilesMatch "\.php$">
    <IfModule mod_rewrite.c>
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} -f
        RewriteCond %{REQUEST_FILENAME} \.php$
        RewriteCond %{REQUEST_URI} !^/index\.php
        RewriteCond %{REQUEST_URI} !^/contact-handler\.php
        RewriteRule ^(.*)$ - [F,L]
    </IfModule>
</FilesMatch>

# Block access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to configuration files
<FilesMatch "\.(conf|config|ini|log|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to version control files
<FilesMatch "\.(git|svn|hg|bzr|cvs)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to package files
<FilesMatch "\.(json|lock|md|txt|yml|yaml)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Allow access to specific files
<Files "index.html">
    Order allow,deny
    Allow from all
</Files>

<Files "contact-us.html">
    Order allow,deny
    Allow from all
</Files>

# Rate limiting (if mod_ratelimit is available)
<IfModule mod_ratelimit.c>
    <Location "/contact-handler.php">
        SetOutputFilter RATE_LIMIT
        SetEnv rate-limit 10
    </Location>
</IfModule>

# Block bad bots and crawlers
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block common bad user agents
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^.*(libwww-perl|curl|wget|python|nikto|scan) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} ^.*(winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Prevent access to server information
ServerTokens Prod
ServerSignature Off

# Disable directory browsing
Options -Indexes

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control for static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Force HTTPS (uncomment if you have SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Handle index redirects first (highest priority)
RewriteRule ^index/?$ / [R=301,L]
RewriteRule ^index\.html/?$ / [R=301,L]

# Remove trailing slash for files but keep for directories
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.+)/$ /$1 [R=301,L]

# Handle clean URLs - remove .html extension from URLs
RewriteCond %{THE_REQUEST} \s/([^.]+)\.html[\s?] [NC]
RewriteRule ^ /%1 [R=301,L]

# Internally rewrite clean URLs to .html files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^([^/]+)/?$ $1.html [L]

# Default directory index
DirectoryIndex index.html

# Error Pages (optional)
ErrorDocument 404 /index.html
